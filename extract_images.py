import fitz
import os

# Ensure the output directory exists
os.makedirs("static/assets", exist_ok=True)

# Open the PDF
doc = fitz.open("media/ILAPS Folleto (Agosto 2025).pdf")

# Extract images from each page
for page_num in range(len(doc)):
    page = doc.load_page(page_num)
    images = page.get_images(full=True)
    for img_index, img in enumerate(images):
        xref = img[0]
        base_image = doc.extract_image(xref)
        image_bytes = base_image["image"]
        image_ext = base_image["ext"]
        image_name = f"brochure_page_{page_num}_img_{img_index}.{image_ext}"
        with open(f"static/assets/{image_name}", "wb") as img_file:
            img_file.write(image_bytes)
        print(f"Extracted {image_name}")

doc.close()
print("Image extraction complete.")