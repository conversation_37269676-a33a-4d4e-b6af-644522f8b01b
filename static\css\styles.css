/* 
 * ILAPS - Instituto Latinoamericano para la Paz y la Seguridad
 * Main Stylesheet
 */

:root {
    /* Colors based on requested palette */
    --primary: #006F9A;         /* Primary blue */
    --primary-dark: #004C6B;    /* Darker blue for hover states */
    --primary-light: #3399CC;   /* Lighter blue accent */
    --secondary: #E4B028;       /* Gold/amber accent */
    --secondary-dark: #B3861F;  /* Darker gold for hover states */
    --secondary-light: #F5C842; /* Lighter gold accent */
    --background-light: #e8ca71; /* Light cream background */
    --neutral-dark: #333333;    /* Dark gray for text */
    --neutral-medium: #666666;  /* Medium gray for secondary text */
    --neutral-light: #f8f5e9;   /* Light cream for backgrounds */
    --white: #ffffff;
    --black: #212121;
    
    /* Typography */
    --body-font: 'Roboto', sans-serif;
    --heading-font: 'Playfair Display', serif;
}

/* Base Styles */
body {
    font-family: var(--body-font);
    color: var(--neutral-dark);
    line-height: 1.6;
    padding-top: 76px; /* To account for fixed navbar */
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--heading-font);
    margin-bottom: 1rem;
    font-weight: 700;
    color: var(--primary-dark);
}

a {
    color: var(--primary);
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.btn-primary {
    background-color: var(--primary);
    border-color: var(--primary);
}

.btn-primary:hover, 
.btn-primary:focus {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-outline-primary {
    color: var(--primary);
    border-color: var(--primary);
}

.btn-outline-primary:hover, 
.btn-outline-primary:focus {
    background-color: var(--primary);
    border-color: var(--primary);
    color: var(--white);
}

.section-title {
    font-size: 2.25rem;
    margin-bottom: 0.5rem;
    position: relative;
    padding-bottom: 1rem;
}

.section-title:after {
    content: "";
    position: absolute;
    width: 70px;
    height: 3px;
    background-color: var(--primary);
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
}

.section-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: var(--neutral-medium);
}

.section-padding {
    padding: 5rem 0;
}

/* Navigation */
.navbar {
    background-color: var(--white);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 0.75rem 0;
    transition: all 0.3s ease;
}

.navbar-brand {
    padding: 0;
}

.nav-logo {
    height: 50px;
    width: auto;
}

.navbar-light .navbar-nav .nav-link {
    color: var(--neutral-dark);
    padding: 0.75rem 1rem;
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-light .navbar-nav .nav-link:hover, 
.navbar-light .navbar-nav .nav-link.active {
    color: var(--primary);
}

.navbar .btn-outline-primary {
    padding: 0.375rem 0.75rem;
}

/* Language Switcher */
.language-switcher {
    display: flex;
    align-items: center;
}

.language-btn {
    display: flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.flag-icon {
    width: 18px;
    height: auto;
    margin-right: 5px;
}

.language-btn.active {
    background-color: var(--primary);
    color: var(--white);
    border-color: var(--primary);
}

/* Hero Section */
.hero-section {
    position: relative;
    height: 100vh;
    min-height: 600px;
    color: var(--white);
    overflow: hidden;
}

.video-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

.video-placeholder {
    width: 100%;
    height: 100%;
    background-color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.7);
    font-style: italic;
    text-align: center;
    padding: 2rem;
}

.video-background {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
}

.overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0,0,0,0.6) 0%, rgba(0,0,0,0.4) 100%);
}

.hero-content {
    position: relative;
    z-index: 1;
    height: 100%;
    display: flex;
    align-items: center;
}

.hero-logo {
    width: 150px;
    height: auto;
    margin-bottom: 2rem;
}

.hero-section h1 {
    font-size: 3rem;
    color: var(--white);
    margin-bottom: 1.5rem;
}

.tagline-container {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#dynamic-tagline {
    font-size: 1.5rem;
    margin-bottom: 2rem;
    animation: fadeInOut 1s ease;
}

@keyframes fadeInOut {
    0% { opacity: 0; transform: translateY(10px); }
    100% { opacity: 1; transform: translateY(0); }
}

/* Page Hero */
.page-hero {
    background-color: var(--primary);
    color: var(--white);
    padding: 5rem 0;
    position: relative;
}

/* Background Colors */
.bg-light {
    background-color: var(--background-light) !important;
}

.page-hero h1 {
    color: var(--white);
}

.page-hero .lead {
    font-size: 1.25rem;
    max-width: 800px;
    margin: 0 auto;
}

/* Cards and Features */
.card {
    border: none;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.card-body {
    padding: 2rem;
}

.card-title {
    color: var(--primary-dark);
    margin-bottom: 1rem;
}

.icon-container {
    color: var(--primary);
    margin-bottom: 1.5rem;
}

.feature-card {
    display: flex;
    flex-direction: column;
    background-color: var(--white);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    height: 100%;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.feature-img-container {
    width: 100%;
    height: 250px;
    overflow: hidden;
}

.feature-content {
    padding: 2rem;
}

.feature-content h3 {
    color: var(--primary-dark);
    margin-bottom: 1rem;
}

/* Image Placeholders */
.img-placeholder {
    background-color: var(--background-light);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--neutral-medium);
    width: 100%;
    height: 100%;
    font-style: italic;
    padding: 2rem;
    border-radius: 4px;
}

/* When img-placeholder contains an actual image, adjust layout */
.img-placeholder:has(img) {
    background-color: transparent;
    display: block;
    padding: 0;
    justify-content: flex-start;
    align-items: flex-start;
}

/* Alternative approach for better browser support - use .img-container class */
.img-container {
    width: 100%;
    overflow: hidden;
    border-radius: 4px;
}

.img-container.large {
    height: 400px;
}

.img-container.medium {
    height: 250px;
}

.img-container.small {
    height: 180px;
}

.img-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

/* Ensure proper image display */
.img-container {
    position: relative;
}

.img-placeholder i {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.img-placeholder.large {
    height: 400px;
}

.img-placeholder.medium {
    height: 250px;
}

.img-placeholder.small {
    height: 180px;
}

.img-placeholder.team {
    height: 250px;
    border-radius: 8px;
    margin-bottom: 1.5rem;
}

.img-placeholder.partner {
    height: 100px;
    width: 180px;
    margin: 1rem;
}

/* News and Events */
.news-item {
    margin-bottom: 2rem;
}

.news-item h4 {
    font-size: 1.2rem;
    margin-bottom: 0.25rem;
}

.text-muted {
    color: var(--neutral-medium);
}

.read-more {
    color: var(--primary);
    font-weight: 500;
    text-decoration: none;
}

.read-more:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.event-item {
    display: flex;
    margin-bottom: 2rem;
}

.event-date {
    min-width: 70px;
    height: 70px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: var(--primary);
    color: var(--white);
    border-radius: 8px;
    margin-right: 1.5rem;
    text-align: center;
}

.event-date .month {
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
}

.event-date .day {
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
    margin-top: 0.25rem;
}

.event-details {
    flex: 1;
}

.event-details h4 {
    margin-bottom: 0.25rem;
}

.partners-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
}

/* Call to Action Section */
.cta-section {
    background-color: var(--secondary);
    color: var(--white);
    padding: 5rem 0;
    position: relative;
}

.cta-section h2 {
    color: var(--white);
    margin-bottom: 1.5rem;
}

.cta-section .lead {
    font-size: 1.25rem;
    max-width: 800px;
    margin: 0 auto 1.5rem auto;
}

.cta-section .btn-primary {
    background-color: var(--white);
    border-color: var(--white);
    color: var(--primary);
}

.cta-section .btn-primary:hover,
.cta-section .btn-primary:focus {
    background-color: rgba(255, 255, 255, 0.9);
    border-color: rgba(255, 255, 255, 0.9);
    color: var(--primary-dark);
}

/* Footer */
.footer {
    background-color: var(--neutral-dark);
    color: var(--white);
}

.footer h5 {
    color: var(--white);
    margin-bottom: 1.5rem;
    font-weight: 700;
}

.footer-logo {
    height: 60px;
    width: auto;
    margin-bottom: 1rem;
}

.footer a {
    color: rgba(255, 255, 255, 0.8);
    transition: color 0.3s ease;
}

.footer a:hover {
    color: var(--white);
    text-decoration: none;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--white);
    transition: background-color 0.3s ease;
}

.social-link:hover {
    background-color: var(--primary-light);
    color: var(--white);
    text-decoration: none;
}

/* About Page */
.timeline {
    position: relative;
    padding: 2rem 0;
}

.timeline:before {
    content: '';
    position: absolute;
    height: 100%;
    width: 2px;
    background-color: var(--primary-light);
    left: 50%;
    transform: translateX(-50%);
}

.timeline-item {
    margin-bottom: 3rem;
    position: relative;
}

.timeline-dot {
    width: 20px;
    height: 20px;
    background-color: var(--primary);
    border-radius: 50%;
    position: absolute;
    left: 50%;
    top: 0;
    transform: translateX(-50%);
    z-index: 1;
}

.timeline-content {
    width: 45%;
    padding: 1.5rem;
    background-color: var(--white);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    position: relative;
    margin-left: auto;
}

.timeline-item:nth-child(even) .timeline-content {
    margin-left: 0;
    margin-right: auto;
}

.team-member {
    text-align: center;
    margin-bottom: 2rem;
}

.team-member h3 {
    font-size: 1.5rem;
    margin: 1rem 0 0.25rem 0;
}

.team-member .position {
    color: var(--primary);
    font-weight: 500;
    margin-bottom: 1rem;
}

.values-list li {
    margin-bottom: 1.5rem;
}

.values-list h4 {
    color: var(--primary);
    display: flex;
    align-items: center;
}

.governance-item {
    margin-bottom: 1.5rem;
}

.governance-item h4 {
    color: var(--primary);
}

.report-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.report-item {
    display: flex;
    align-items: center;
    background-color: var(--white);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
}

.report-icon {
    font-size: 2.5rem;
    color: var(--primary);
    margin-right: 1.5rem;
}

.report-content {
    flex: 1;
}

.report-content h3 {
    margin-bottom: 0.5rem;
}

/* What We Do Page */
.approach-list {
    list-style-type: none;
    padding: 0;
}

.approach-list li {
    margin-bottom: 0.75rem;
    position: relative;
    padding-left: 1.5rem;
}

.approach-list li:before {
    content: "→";
    color: var(--primary);
    position: absolute;
    left: 0;
    top: 0;
}

.program-card {
    padding: 2rem;
    background-color: var(--white);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.program-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.program-icon {
    color: var(--primary);
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
}

.program-card h3 {
    margin-bottom: 1rem;
}

.program-card ul {
    list-style-type: none;
    padding: 0;
    margin-top: 1rem;
}

.program-card ul li {
    margin-bottom: 0.5rem;
    position: relative;
    padding-left: 1.25rem;
}

.program-card ul li:before {
    content: "•";
    color: var(--primary);
    position: absolute;
    left: 0;
    top: 0;
}

.initiative-card {
    display: flex;
    flex-direction: column;
    background-color: var(--white);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    height: 100%;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.initiative-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.initiative-img {
    width: 100%;
    height: 220px;
    overflow: hidden;
}

.initiative-content {
    padding: 2rem;
    flex: 1;
}

.initiative-content h3 {
    margin-bottom: 0.25rem;
}

.location {
    color: var(--neutral-medium);
    margin-bottom: 1rem;
}

.initiative-stats {
    display: flex;
    justify-content: space-between;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.stat-item {
    text-align: center;
    flex: 1;
}

.stat-number {
    display: block;
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--neutral-medium);
}

.impact-stats {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 3rem;
}

.impact-stat {
    text-align: center;
    flex: 0 0 auto;
    width: 200px;
}

.impact-stat .stat-number {
    font-size: 3rem;
    color: var(--primary);
    margin-bottom: 0.5rem;
}

.impact-stat .stat-text {
    font-size: 1.1rem;
}

.impact-highlight {
    margin-bottom: 2rem;
    background-color: var(--white);
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.resource-card {
    text-align: center;
    padding: 2.5rem 1.5rem;
    background-color: var(--white);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.resource-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.resource-icon {
    font-size: 3rem;
    color: var(--primary);
    margin-bottom: 1.5rem;
}

.resource-card h3 {
    margin-bottom: 1rem;
}

.resource-card p {
    margin-bottom: 1.5rem;
}

.feature-list {
    list-style-type: none;
    padding: 0;
}

.feature-list li {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.feature-list li i {
    color: var(--primary);
    margin-right: 1rem;
    margin-top: 0.25rem;
}

.event-types {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.event-type-tag {
    background-color: var(--primary-light);
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.875rem;
}

/* Events Page */
.featured-event {
    background-color: var(--white);
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    margin-bottom: 3rem;
}

.event-badge {
    display: inline-block;
    background-color: var(--primary);
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 1rem;
}

.event-meta {
    color: var(--neutral-medium);
    margin-bottom: 1.5rem;
}

.event-meta p {
    margin-bottom: 0.5rem;
}

.event-card {
    position: relative;
    background-color: var(--white);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.event-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.event-card .event-date {
    position: absolute;
    top: 1rem;
    left: 1rem;
    z-index: 1;
}

.event-card .event-img {
    height: 200px;
    overflow: hidden;
}

.event-card .event-content {
    padding: 2rem;
}

.event-card h3 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
}

.event-location {
    color: var(--neutral-medium);
    margin-bottom: 1rem;
}

.event-details {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 1rem;
    font-size: 0.875rem;
    color: var(--neutral-medium);
}

.detail-item {
    display: flex;
    align-items: center;
}

.detail-item i {
    margin-right: 0.5rem;
}

.past-events-list {
    list-style-type: none;
    padding: 0;
}

.past-events-list li {
    display: flex;
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.past-events-list li:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.past-event-date {
    min-width: 120px;
    font-weight: 500;
    color: var(--primary);
}

.past-event-content {
    flex: 1;
}

.past-event-content h4 {
    margin-bottom: 0.25rem;
}

.past-event-content p {
    margin-bottom: 0.5rem;
    color: var(--neutral-medium);
}

.past-event-links {
    display: flex;
    gap: 1rem;
}

.past-event-links a {
    font-size: 0.875rem;
}

.calendar-container {
    background-color: var(--white);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 2rem;
}

/* News Page */
.featured-news {
    background-color: var(--white);
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    margin-bottom: 3rem;
}

.news-badge {
    display: inline-block;
    background-color: var(--primary);
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 1rem;
}

.news-meta {
    color: var(--neutral-medium);
    margin-bottom: 1.5rem;
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.news-card {
    background-color: var(--white);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.news-card .news-img {
    height: 200px;
    overflow: hidden;
}

.news-card .news-content {
    padding: 2rem;
}

.news-card h3 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
}

.news-categories {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 2rem;
}

.news-categories .nav-link {
    color: var(--neutral-dark);
    background-color: var(--white);
    border-radius: 50px;
    padding: 0.5rem 1.5rem;
    transition: all 0.3s ease;
}

.news-categories .nav-link.active {
    background-color: var(--primary);
    color: var(--white);
}

.news-categories .nav-link:hover:not(.active) {
    background-color: var(--neutral-light);
}

.press-releases {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.press-release-item {
    display: flex;
    background-color: var(--white);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
}

.press-release-date {
    min-width: 120px;
    font-weight: 500;
    color: var(--primary);
}

.press-release-content {
    flex: 1;
}

.press-release-content h3 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
}

.media-coverage {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.media-item {
    display: flex;
    flex-direction: column;
    background-color: var(--white);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
}

.media-source {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.source-name {
    font-weight: 700;
    color: var(--primary);
}

.source-date {
    color: var(--neutral-medium);
}

.media-content h3 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
}

.newsletter-features {
    list-style-type: none;
    padding: 0;
    margin: 1.5rem 0;
}

.newsletter-features li {
    margin-bottom: 0.75rem;
}

.newsletter-features li i {
    color: var(--primary);
}

.newsletter-archive {
    background-color: var(--white);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 2rem;
}

.newsletter-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.newsletter-item {
    display: flex;
    align-items: center;
}

.newsletter-icon {
    font-size: 2rem;
    color: var(--primary);
    margin-right: 1.5rem;
}

.newsletter-content h4 {
    margin-bottom: 0.25rem;
}

.newsletter-content p {
    margin-bottom: 0.5rem;
    color: var(--neutral-medium);
}

/* Contact Page */
.contact-info-card {
    background-color: var(--white);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 2rem;
    height: 100%;
}

.contact-info-card h3 {
    margin-bottom: 1.5rem;
    color: var(--primary-dark);
}

.contact-info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.contact-info-item i {
    font-size: 1.5rem;
    color: var(--primary);
    margin-right: 1rem;
    min-width: 1.5rem;
}

.contact-info-item p {
    margin-bottom: 0;
}

.contact-form-card {
    background-color: var(--white);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 2rem;
}

.map-container {
    height: 400px;
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

#map {
    width: 100%;
    height: 400px;
    min-height: 400px;
    background-color: #f8f9fa;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.map-error {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8d7da;
    color: #721c24;
    text-align: center;
    padding: 20px;
}

.office-locations {
    margin-top: 4rem;
}

.office-card {
    background-color: var(--white);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
    height: 100%;
}

.office-card h4 {
    color: var(--primary);
    margin-bottom: 1rem;
}

.office-card address {
    margin-bottom: 1rem;
}

.faq-section .accordion-item {
    border: none;
    margin-bottom: 1rem;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.faq-section .accordion-button {
    padding: 1.5rem;
    font-weight: 600;
    color: var(--primary-dark);
    background-color: var(--white);
}

.faq-section .accordion-button:not(.collapsed) {
    background-color: var(--white);
    color: var(--primary);
}

.faq-section .accordion-button:focus {
    box-shadow: none;
    border-color: rgba(0, 0, 0, 0.125);
}

.faq-section .accordion-body {
    padding: 1.5rem;
    background-color: var(--white);
}

.faq-categories {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 2rem;
}

.faq-categories .nav-link {
    color: var(--neutral-dark);
    background-color: var(--white);
    border-radius: 50px;
    padding: 0.5rem 1.5rem;
    transition: all 0.3s ease;
}

.faq-categories .nav-link.active {
    background-color: var(--primary);
    color: var(--white);
}

.faq-categories .nav-link:hover:not(.active) {
    background-color: var(--neutral-light);
}

/* Blog Page */
.blog-post-card {
    background-color: var(--white);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    margin-bottom: 2rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.blog-post-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.blog-post-img {
    height: 250px;
    overflow: hidden;
}

.blog-post-content {
    padding: 2rem;
}

.blog-post-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    color: var(--neutral-medium);
}

.meta-item {
    display: flex;
    align-items: center;
}

.meta-item i {
    margin-right: 0.5rem;
}

.blog-post-content h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.blog-categories {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 2rem;
}

.blog-categories .nav-link {
    color: var(--neutral-dark);
    background-color: var(--white);
    border-radius: 50px;
    padding: 0.5rem 1.5rem;
    transition: all 0.3s ease;
}

.blog-categories .nav-link.active {
    background-color: var(--primary);
    color: var(--white);
}

.blog-categories .nav-link:hover:not(.active) {
    background-color: var(--neutral-light);
}

.blog-sidebar {
    margin-bottom: 2rem;
}

.sidebar-widget {
    background-color: var(--white);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.sidebar-widget h3 {
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 0.75rem;
}

.sidebar-widget h3:after {
    content: "";
    position: absolute;
    width: 50px;
    height: 3px;
    background-color: var(--primary);
    bottom: 0;
    left: 0;
}

.popular-posts-list {
    list-style-type: none;
    padding: 0;
}

.popular-posts-list li {
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.popular-posts-list li:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.popular-posts-list h4 {
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag {
    background-color: var(--neutral-light);
    color: var(--neutral-dark);
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.tag:hover {
    background-color: var(--primary);
    color: var(--white);
    text-decoration: none;
}

/* Privacy Policy and Legal Pages */
.legal-content {
    background-color: var(--white);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 2.5rem;
}

.legal-content h2 {
    color: var(--primary-dark);
    margin-top: 2.5rem;
    margin-bottom: 1.5rem;
}

.legal-content h2:first-child {
    margin-top: 0;
}

.legal-content ul,
.legal-content ol {
    margin-bottom: 1.5rem;
}

.legal-content li {
    margin-bottom: 0.75rem;
}

/* Search Results */
.search-result-item {
    background-color: var(--white);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.search-result-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.search-result-item h3 {
    margin-bottom: 0.5rem;
}

.search-result-item .result-type {
    display: inline-block;
    background-color: var(--primary-light);
    color: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    margin-bottom: 1rem;
}

/* Modal */
.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
    border-bottom: none;
    padding: 1.5rem 1.5rem 0.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: none;
    padding: 0.5rem 1.5rem 1.5rem;
}

/* Responsive Adjustments */
@media (max-width: 1199.98px) {
    .hero-section h1 {
        font-size: 2.5rem;
    }
    
    #dynamic-tagline {
        font-size: 1.25rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
}

@media (max-width: 991.98px) {
    .hero-section {
        height: auto;
        min-height: 500px;
    }
    
    .hero-content {
        padding: 5rem 0;
    }
    
    .timeline:before {
        left: 30px;
    }
    
    .timeline-dot {
        left: 30px;
    }
    
    .timeline-content {
        width: calc(100% - 60px);
        margin-left: 60px;
        margin-right: 0;
    }
    
    .timeline-item:nth-child(even) .timeline-content {
        margin-left: 60px;
        margin-right: 0;
    }
    
    .past-events-list li {
        flex-direction: column;
    }
    
    .past-event-date {
        margin-bottom: 0.5rem;
    }
    
    .press-release-item {
        flex-direction: column;
    }
    
    .press-release-date {
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 767.98px) {
    .section-padding {
        padding: 3rem 0;
    }
    
    .hero-section h1 {
        font-size: 2rem;
    }
    
    #dynamic-tagline {
        font-size: 1.1rem;
    }
    
    .hero-logo {
        width: 120px;
    }
    
    .page-hero {
        padding: 3rem 0;
    }
    
    .featured-event .row,
    .featured-news .row {
        flex-direction: column;
    }
    
    .event-item {
        flex-direction: column;
    }
    
    .event-date {
        margin-bottom: 1rem;
        margin-right: 0;
    }
    
    .impact-stats {
        gap: 1.5rem;
    }
    
    .impact-stat {
        width: 150px;
    }
    
    .impact-stat .stat-number {
        font-size: 2.5rem;
    }
    
    .initiative-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .newsletter-item {
        flex-direction: column;
        text-align: center;
    }
    
    .newsletter-icon {
        margin-right: 0;
        margin-bottom: 1rem;
    }
}

@media (max-width: 575.98px) {
    .hero-section h1 {
        font-size: 1.75rem;
    }
    
    .section-title {
        font-size: 1.75rem;
    }
    
    .section-subtitle {
        font-size: 1rem;
    }
    
    .nav-logo {
        height: 40px;
    }
    
    .hero-logo {
        width: 100px;
    }
    
    .tagline-container {
        height: 50px;
    }
    
    #dynamic-tagline {
        font-size: 1rem;
    }
    
    .social-links {
        justify-content: center;
    }
}
