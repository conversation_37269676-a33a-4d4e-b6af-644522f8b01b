import os
from flask import Flask, url_for
from whitenoise import WhiteNoise

# Create Flask app
app = Flask(__name__, static_folder='static', static_url_path='/static')
app.secret_key = os.environ.get("SESSION_SECRET", "ilaps-default-secret-key")

# Configure WhiteNoise
app.wsgi_app = WhiteNoise(app.wsgi_app, 
    root='static/',
    prefix='static/',
    index_file=True, 
    autorefresh=True
)

# Import routes after app is created to avoid circular imports
from routes import *

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=int(os.environ.get("PORT", 5000)))
