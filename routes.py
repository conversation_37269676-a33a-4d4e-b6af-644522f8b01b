from flask import render_template, request, jsonify
from app import app


@app.route('/')
def index():
    return render_template('index.html', active_page='home')


@app.route('/about')
def about():
    return render_template('about.html', active_page='about')


@app.route('/what-we-do')
def what_we_do():
    return render_template('what_we_do.html', active_page='what_we_do')


@app.route('/events')
def events():
    return render_template('events.html', active_page='events')


@app.route('/news')
def news():
    return render_template('news.html', active_page='news')


@app.route('/contact')
def contact():
    return render_template('contact.html', active_page='contact')


@app.route('/privacy-policy')
def privacy_policy():
    return render_template('privacy_policy.html')


@app.route('/faq')
def faq():
    return render_template('faq.html')


@app.route('/blog')
def blog():
    return render_template('blog.html')


@app.route('/submit-contact', methods=['POST'])
def submit_contact():
    if request.method == 'POST':
        name = request.form.get('name')
        email = request.form.get('email')
        message = request.form.get('message')

        # In a real application, you would process this data
        # (e.g., send an email, store in database)

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({
                'success':
                True,
                'message':
                'Thank you for your message. We will get back to you soon.'
            })

        return render_template(
            'contact.html',
            success=True,
            message='Thank you for your message. We will get back to you soon.'
        )

    return render_template('contact.html')
