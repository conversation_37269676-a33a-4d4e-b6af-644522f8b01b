{% extends "layout.html" %}

{% block title %}Contact Us - ILAPS{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="page-hero">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h1 data-i18n="contact_title">Contact Us</h1>
                <p class="lead" data-i18n="contact_subtitle">Reach out to learn more about our work or how to get involved with ILAPS</p>
            </div>
        </div>
    </div>
</section>

<!-- Contact Information -->
<section class="section-padding">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5">
                <h2 class="section-title" data-i18n="get_in_touch">Get in Touch</h2>
                <p class="section-subtitle" data-i18n="get_in_touch_subtitle">We welcome your questions, comments, and inquiries</p>
            </div>
        </div>
        <div class="row">
            <div class="col-md-5 mb-4 mb-md-0">
                <div class="contact-info-card h-100">
                    <h3 data-i18n="contact_information">Contact Information</h3>
                    <div class="contact-info-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <div>
                            <h4 data-i18n="headquarters">Headquarters</h4>
                            <p>Cra. 6 #26B- 85 Oficina #<br>Bogotá, Colombia</p>
                        </div>
                    </div>
                    <div class="contact-info-item">
                        <i class="fas fa-phone"></i>
                        <div>
                            <h4 data-i18n="phone">Phone</h4>
                            <p>+57 (1) 745-8913<br>+57 (*************</p>
                        </div>
                    </div>
                    <div class="contact-info-item">
                        <i class="fas fa-envelope"></i>
                        <div>
                            <h4 data-i18n="email">Email</h4>
                            <p><EMAIL><br><EMAIL></p>
                        </div>
                    </div>
                    <div class="contact-info-item">
                        <i class="fas fa-clock"></i>
                        <div>
                            <h4 data-i18n="working_hours">Working Hours</h4>
                            <p>Monday – Friday: 8:30 AM – 5:30 PM<br>Saturday & Sunday: Closed</p>
                        </div>
                    </div>
                    <div class="social-links mt-4">
                        <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
            </div>
            <div class="col-md-7">
                <div class="contact-form-card">
                    <h3 data-i18n="send_message">Send Us a Message</h3>
                    {% if success %}
                    <div class="alert alert-success">
                        {{ message }}
                    </div>
                    {% endif %}
                    <form id="contact-form" action="/submit-contact" method="post">
                        <div class="mb-3">
                            <label for="name" class="form-label" data-i18n="name">Name</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label" data-i18n="email">Email</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        <div class="mb-3">
                            <label for="subject" class="form-label" data-i18n="subject">Subject</label>
                            <input type="text" class="form-control" id="subject" name="subject" required>
                        </div>
                        <div class="mb-3">
                            <label for="message" class="form-label" data-i18n="message">Message</label>
                            <textarea class="form-control" id="message" name="message" rows="5" required></textarea>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="privacy-consent" required>
                            <label class="form-check-label" for="privacy-consent">
                                <span data-i18n="privacy_consent">I agree to the processing of my personal data in accordance with the</span>
                                <a href="/privacy-policy" target="_blank" data-i18n="privacy_policy">Privacy Policy</a>.
                            </label>
                        </div>
                        <button type="submit" class="btn btn-primary" data-i18n="submit">Submit</button>
                    </form>
                    <div id="form-feedback" class="mt-3" style="display: none;"></div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Map Section -->
<section class="section-padding bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-10 mx-auto">
                <div id="map" style="width: 100%; height: 400px;"></div>
            </div>
        </div>
    </div>
</section>

<!-- Initialize the Maps API with the new method -->
<script>
    (g=>{var h,a,k,p="The Google Maps JavaScript API",c="google",l="importLibrary",q="__ib__",m=document,b=window;b=b[c]||(b[c]={});var d=b.maps||(b.maps={}),r=new Set,e=new URLSearchParams,u=()=>h||(h=new Promise(async(f,n)=>{await (a=m.createElement("script"));e.set("libraries",[...r]+"");for(k in g)e.set(k.replace(/[A-Z]/g,t=>"_"+t[0].toLowerCase()),g[k]);e.set("callback",c+".maps."+q);a.src=`https://maps.${c}apis.com/maps/api/js?`+e;d[q]=f;a.onerror=()=>h=n(Error(p+" could not load."));a.nonce=m.querySelector("script[nonce]")?.nonce||"";m.head.append(a)}));d[l]?console.warn(p+" only loads once. Ignoring:",g):d[l]=(f,...n)=>r.add(f)&&u().then(()=>d[l](f,...n))})({
        key: "AIzaSyAJiPIfHxWqcXhuM5yzyx3ozIyCqJXsy9E",
        v: "weekly"
    });
</script>

<!-- Map initialization script -->
<script>
    let map;

    async function initMap() {
        // ILAPS headquarters location 4.615404070854692, -74.0685102803958
        const position = { lat: 4.615404070854692, lng: -74.0685102803958 };

        // Request needed libraries
        const { Map } = await google.maps.importLibrary("maps");
        const { AdvancedMarkerElement } = await google.maps.importLibrary("marker");

        // Initialize the map
        map = new Map(document.getElementById("map"), {
            zoom: 16,
            center: position,
            mapId: "ILAPS_MAP",
            mapTypeControl: true,
            streetViewControl: true,
            fullscreenControl: true,
            zoomControl: true
        });

        // Add the marker
        const marker = new AdvancedMarkerElement({
            map: map,
            position: position,
            title: "ILAPS Headquarters"
        });

        // Add info window
        const infoWindow = new google.maps.InfoWindow({
            content: `
                <div style="padding: 10px;">
                    <h5 style="margin: 0 0 5px 0;">ILAPS Headquarters</h5>
                    <p style="margin: 0;">Cra. 6 #26B- 85 Piso 1<br>Bogotá, Colombia</p>
                </div>
            `
        });

        // Add click listener to marker
        marker.addListener('click', () => {
            infoWindow.open(map, marker);
        });
    }

    // Initialize the map
    initMap();
</script>

<!-- Regional Offices -->
<section class="section-padding office-locations">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5">
                <h2 class="section-title">Regional Offices</h2>
                <p class="section-subtitle">Our presence across Latin America</p>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="office-card h-100">
                    <h4>Colombia - Headquarters</h4>
                    <address>
                        Calle 100 #9A-45, Torre 2, Oficina 801<br>
                        Bogotá, Colombia<br>
                    </address>
                    <p><i class="fas fa-phone me-2"></i> +57 (1) 745-8913</p>
                    <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="office-card h-100">
                    <h4>Mexico Office</h4>
                    <address>
                        Av. Paseo de la Reforma 222, Piso 10<br>
                        Juárez, Cuauhtémoc<br>
                        06600 Ciudad de México, CDMX
                    </address>
                    <p><i class="fas fa-phone me-2"></i> +52 (55) 5080-7600</p>
                    <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="office-card h-100">
                    <h4>Brazil Office</h4>
                    <address>
                        Av. Paulista, 1000, 10° andar<br>
                        Bela Vista<br>
                        São Paulo - SP, 01310-100
                    </address>
                    <p><i class="fas fa-phone me-2"></i> +55 (11) 3145-8000</p>
                    <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="office-card h-100">
                    <h4>Chile Office</h4>
                    <address>
                        Av. Apoquindo 4800, Piso 8<br>
                        Las Condes<br>
                        Santiago, Chile
                    </address>
                    <p><i class="fas fa-phone me-2"></i> +56 (2) 2928-3700</p>
                    <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="office-card h-100">
                    <h4>Argentina Office</h4>
                    <address>
                        Av. del Libertador 498, Piso 3<br>
                        C1001ABR<br>
                        Buenos Aires, Argentina
                    </address>
                    <p><i class="fas fa-phone me-2"></i> +54 (11) 4312-9000</p>
                    <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="office-card h-100">
                    <h4>Peru Office</h4>
                    <address>
                        Av. Jorge Basadre 310, Piso 8<br>
                        San Isidro<br>
                        Lima, Peru
                    </address>
                    <p><i class="fas fa-phone me-2"></i> +51 (1) 611-7800</p>
                    <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Department Contacts -->
<section class="section-padding bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5">
                <h2 class="section-title">Department Contacts</h2>
                <p class="section-subtitle">Reach out to specific departments for specialized inquiries</p>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-10 mx-auto">
                <div class="accordion" id="departmentAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingPrograms">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapsePrograms" aria-expanded="true" aria-controls="collapsePrograms">
                                Programs & Initiatives
                            </button>
                        </h2>
                        <div id="collapsePrograms" class="accordion-collapse collapse show" aria-labelledby="headingPrograms" data-bs-parent="#departmentAccordion">
                            <div class="accordion-body">
                                <p>For inquiries about our programs, initiatives, or to explore potential collaborations:</p>
                                <p><strong>Contact:</strong> Maria Sanchez, Director of Programs</p>
                                <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                                <p><i class="fas fa-phone me-2"></i> +57 (1) 745-8913 ext. 101</p>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingResearch">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseResearch" aria-expanded="false" aria-controls="collapseResearch">
                                Research & Publications
                            </button>
                        </h2>
                        <div id="collapseResearch" class="accordion-collapse collapse" aria-labelledby="headingResearch" data-bs-parent="#departmentAccordion">
                            <div class="accordion-body">
                                <p>For research inquiries, data requests, or to discuss potential research partnerships:</p>
                                <p><strong>Contact:</strong> Prof. Carlos Mendez, Research Director</p>
                                <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                                <p><i class="fas fa-phone me-2"></i> +57 (1) 745-8913 ext. 102</p>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingMedia">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseMedia" aria-expanded="false" aria-controls="collapseMedia">
                                Media & Communications
                            </button>
                        </h2>
                        <div id="collapseMedia" class="accordion-collapse collapse" aria-labelledby="headingMedia" data-bs-parent="#departmentAccordion">
                            <div class="accordion-body">
                                <p>For media inquiries, interview requests, or to receive our press releases:</p>
                                <p><strong>Contact:</strong> Ana Luisa Vega, Director of Communications</p>
                                <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                                <p><i class="fas fa-phone me-2"></i> +57 (1) 745-8913 ext. 103</p>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingDonations">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseDonations" aria-expanded="false" aria-controls="collapseDonations">
                                Donations & Partnerships
                            </button>
                        </h2>
                        <div id="collapseDonations" class="accordion-collapse collapse" aria-labelledby="headingDonations" data-bs-parent="#departmentAccordion">
                            <div class="accordion-body">
                                <p>For information about supporting ILAPS through donations or institutional partnerships:</p>
                                <p><strong>Contact:</strong> Luis Morales, Director of Development</p>
                                <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                                <p><i class="fas fa-phone me-2"></i> +57 (1) 745-8913 ext. 104</p>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingEvents">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseEvents" aria-expanded="false" aria-controls="collapseEvents">
                                Events & Training
                            </button>
                        </h2>
                        <div id="collapseEvents" class="accordion-collapse collapse" aria-labelledby="headingEvents" data-bs-parent="#departmentAccordion">
                            <div class="accordion-body">
                                <p>For information about upcoming events, workshops, or training programs:</p>
                                <p><strong>Contact:</strong> Dr. Miguel Santos, Director of Education</p>
                                <p><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                                <p><i class="fas fa-phone me-2"></i> +57 (1) 745-8913 ext. 105</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action -->
<section class="cta-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2>Stay Connected</h2>
                <p class="lead mb-4">Subscribe to our newsletter to receive updates on our work and upcoming events.</p>
                <form class="subscription-form">
                    <div class="row justify-content-center">
                        <div class="col-md-6">
                            <div class="input-group mb-3">
                                <input type="email" class="form-control" placeholder="Your email address" aria-label="Your email address">
                                <button class="btn btn-primary" type="button">Subscribe</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>
{% endblock %}

