# Virtual Environment
ilaps_venv/
venv/
env/
ENV/
.env
.venv
env.bak/
venv.bak/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Flask
instance/
.webassets-cache

# Pytest
.pytest_cache/
.coverage
htmlcov/
coverage.xml
*.cover

# Jupyter Notebook
.ipynb_checkpoints

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database
*.sqlite
*.db

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local configuration
.env.local
.env.development.local
.env.test.local
.env.production.local

# Replit specific
.replit
.breakpoints
replit.nix
.config/
