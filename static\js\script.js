/**
 * ILAPS - Instituto Latinoamericano para la Paz y la Seguridad
 * Main JavaScript file
 */

console.log('Script loaded');

document.addEventListener('DOMContentLoaded', function() {
    // Language translations
    const translations = {
        en: {
            // Navigation
            'nav_home': 'Home',
            'nav_about': 'About Us',
            'nav_what_we_do': 'What We Do',
            'nav_events': 'Events',
            'nav_news': 'Latest News',
            'nav_contact': 'Contact',
            
            // Hero Section
            'hero_title': 'Instituto Latinoamericano para la Paz y la Seguridad',
            'tagline_1': 'Building peace through dialogue and cooperation',
            'tagline_2': 'Transforming conflict into opportunity for peace',
            'tagline_3': 'Supporting security and stability across Latin America',
            'tagline_4': 'Empowering communities to build lasting peace',
            'tagline_5': 'Promoting justice, security, and human rights',
            'btn_learn_more': 'Learn More',
            'btn_get_involved': 'Get Involved',
            
            // Mission Section
            'mission_title': 'Our Mission',
            'mission_subtitle': 'Working towards a more peaceful and secure Latin America',
            'conflict_resolution': 'Conflict Resolution',
            'conflict_resolution_text': 'Facilitating dialogue and promoting peaceful resolution of political and social conflicts across Latin America.',
            'community_building': 'Community Building',
            'community_building_text': 'Strengthening local communities through education, empowerment, and grassroots peace initiatives.',
            'education_research': 'Education & Research',
            'education_research_text': 'Conducting research and providing educational programs on peace, security, and conflict resolution.',
            
            // Featured Initiatives
            'initiatives_title': 'Featured Initiatives',
            'initiatives_subtitle': 'Discover our key programs making an impact across the region',
            'peace_colombia': 'Peace Building in Colombia',
            'peace_colombia_text': 'Supporting the peace process through community dialogue, reconciliation workshops, and sustainable development projects.',
            'security_forum': 'Latin American Security Forum',
            'security_forum_text': 'Annual gathering of experts, policymakers, and civil society to address regional security challenges and coordinate solutions.',
            
            // News & Events
            'news_events_title': 'Latest News & Events',
            'news_events_subtitle': 'Stay updated with our recent activities and upcoming events',
            'news_title': 'News',
            'events_title': 'Upcoming Events',
            'view_all_news': 'View All News',
            'view_all_events': 'View All Events',
            'read_more': 'Read More',
            'register': 'Register',
            
            // Partners
            'partners_title': 'Our Partners & Supporters',
            'partners_subtitle': 'Collaborative efforts for a more peaceful Latin America',
            
            // Call to Action
            'cta_title': 'Join Us in Building a Peaceful Latin America',
            'cta_text': 'Together we can create a future of peace, security, and prosperity for all.',
            'cta_subtitle': 'Together we can create a future of peace, security, and prosperity for all.',
            'get_involved': 'Get Involved',
            
            // Footer
            'footer_text': 'Working towards a peaceful and secure Latin America through dialogue, education, and cooperation.',
            'quick_links': 'Quick Links',
            'connect_with_us': 'Connect With Us',
            'copyright': '© 2023 ILAPS - Instituto Latinoamericano para la Paz y la Seguridad. All rights reserved.',
            
            // Contact Form
            'contact_us': 'Contact Us',
            'name': 'Name',
            'email': 'Email',
            'message': 'Message',
            'submit': 'Submit',
            'customer_support': 'Customer Support',

            // About Page
            'about_ilaps_title': 'About ILAPS',
            'about_ilaps_subtitle': 'Learn about our mission, vision, and the team behind the Instituto Latinoamericano para la Paz y la Seguridad',
            'our_mission': 'Our Mission',
            'mission_text_1': 'The Instituto Latinoamericano para la Paz y la Seguridad (ILAPS) is dedicated to promoting peace, security, and stability across Latin America through dialogue, education, research, and community engagement.',
            'mission_text_2': 'We work to address the root causes of conflict, violence, and insecurity in the region, fostering collaborative solutions that respect human rights and promote sustainable development.',
            'our_vision': 'Our Vision',
            'vision_text': 'We envision a Latin America where all communities live in peace and security, where conflicts are resolved through dialogue rather than violence, and where citizens actively participate in building inclusive, just, and democratic societies.',
            'mission_image': 'Mission Image',

            // Contact Page
            'contact_title': 'Contact Us',
            'contact_subtitle': 'Reach out to learn more about our work or how to get involved with ILAPS',
            'get_in_touch': 'Get in Touch',
            'get_in_touch_subtitle': 'We welcome your questions, comments, and inquiries',
            'contact_information': 'Contact Information',
            'headquarters': 'Headquarters',
            'phone': 'Phone',
            'working_hours': 'Working Hours',
            'working_hours_text': 'Monday – Friday: 8:30 AM – 5:30 PM\nSaturday & Sunday: Closed',
            'send_message': 'Send Us a Message',
            'subject': 'Subject',
            'privacy_consent': 'I agree to the processing of my personal data in accordance with the',
            'privacy_policy': 'Privacy Policy',
            'map_placeholder': 'Interactive map showing ILAPS headquarters and regional offices',
            'regional_offices': 'Regional Offices',
            'regional_offices_subtitle': 'Our presence across Latin America',
            'colombia_office': 'Colombia - Headquarters',
            'mexico_office': 'Mexico Office',

            // FAQ Page
            'faq_title': 'Frequently Asked Questions',
            'faq_subtitle': 'Find answers to common questions about ILAPS and our work',
            'didnt_find_answer': 'Didn\'t Find Your Answer?',
            'didnt_find_answer_text': 'If you couldn\'t find the information you were looking for, please feel free to contact us directly. Our team is ready to assist you.',
            'email_us': 'Email Us',
            'call_us': 'Call Us',

            // Blog Page
            'blog_title': 'Latest News & Insights',
            'blog_subtitle': 'Stay informed about our work and impact across Latin America',
            'read_more': 'Read More',
            'categories': 'Categories',
            'recent_posts': 'Recent Posts',
            'archives': 'Archives'
        },
        es: {
            // Navigation
            'nav_home': 'Inicio',
            'nav_about': 'Acerca de',
            'nav_what_we_do': 'Qué Hacemos',
            'nav_events': 'Eventos',
            'nav_news': 'Últimas Noticias',
            'nav_contact': 'Contacto',
            
            // Hero Section
            'hero_title': 'Instituto Latinoamericano para la Paz y la Seguridad',
            'tagline_1': 'Construyendo paz a través del diálogo y la cooperación',
            'tagline_2': 'Transformando conflictos en oportunidades para la paz',
            'tagline_3': 'Apoyando la seguridad y estabilidad en América Latina',
            'tagline_4': 'Empoderando comunidades para construir paz duradera',
            'tagline_5': 'Promoviendo justicia, seguridad y derechos humanos',
            'btn_learn_more': 'Más Información',
            'btn_get_involved': 'Participar',
            
            // Mission Section
            'mission_title': 'Nuestra Misión',
            'mission_subtitle': 'Trabajando por una América Latina más pacífica y segura',
            'conflict_resolution': 'Resolución de Conflictos',
            'conflict_resolution_text': 'Facilitando el diálogo y promoviendo la resolución pacífica de conflictos políticos y sociales en toda América Latina.',
            'community_building': 'Construcción Comunitaria',
            'community_building_text': 'Fortaleciendo comunidades locales a través de la educación, el empoderamiento y las iniciativas de paz en la base.',
            'education_research': 'Educación e Investigación',
            'education_research_text': 'Realizando investigaciones y proporcionando programas educativos sobre paz, seguridad y resolución de conflictos.',
            
            // Featured Initiatives
            'initiatives_title': 'Iniciativas Destacadas',
            'initiatives_subtitle': 'Descubra nuestros programas clave que están generando un impacto en la región',
            'peace_colombia': 'Construcción de Paz en Colombia',
            'peace_colombia_text': 'Apoyando el proceso de paz a través del diálogo comunitario, talleres de reconciliación y proyectos de desarrollo sostenible.',
            'security_forum': 'Foro de Seguridad Latinoamericano',
            'security_forum_text': 'Reunión anual de expertos, responsables políticos y sociedad civil para abordar los desafíos de seguridad regionales y coordinar soluciones.',
            
            // News & Events
            'news_events_title': 'Últimas Noticias y Eventos',
            'news_events_subtitle': 'Manténgase actualizado con nuestras actividades recientes y próximos eventos',
            'news_title': 'Noticias',
            'events_title': 'Próximos Eventos',
            'view_all_news': 'Ver Todas las Noticias',
            'view_all_events': 'Ver Todos los Eventos',
            'read_more': 'Leer Más',
            'register': 'Inscribirse',
            
            // Partners
            'partners_title': 'Nuestros Socios y Colaboradores',
            'partners_subtitle': 'Esfuerzos colaborativos para una América Latina más pacífica',
            
            // Call to Action
            'cta_title': 'Únase a Nosotros en la Construcción de una América Latina Pacífica',
            'cta_text': 'Juntos podemos crear un futuro de paz, seguridad y prosperidad para todos.',
            'cta_subtitle': 'Juntos podemos crear un futuro de paz, seguridad y prosperidad para todos.',
            'get_involved': 'Participar',
            
            // Footer
            'footer_text': 'Trabajando por una América Latina pacífica y segura a través del diálogo, la educación y la cooperación.',
            'quick_links': 'Enlaces Rápidos',
            'connect_with_us': 'Conecte con Nosotros',
            'copyright': '© 2023 ILAPS - Instituto Latinoamericano para la Paz y la Seguridad. Todos los derechos reservados.',
            
            // Contact Form
            'contact_us': 'Contáctenos',
            'name': 'Nombre',
            'email': 'Correo Electrónico',
            'message': 'Mensaje',
            'submit': 'Enviar',
            'customer_support': 'Atención al Cliente',

            // About Page
            'about_ilaps_title': 'Sobre ILAPS',
            'about_ilaps_subtitle': 'Conozca nuestra misión, visión y el equipo detrás del Instituto Latinoamericano para la Paz y la Seguridad',
            'our_mission': 'Nuestra Misión',
            'mission_text_1': 'El Instituto Latinoamericano para la Paz y la Seguridad (ILAPS) está dedicado a promover la paz, la seguridad y la estabilidad en América Latina a través del diálogo, la educación, la investigación y el compromiso comunitario.',
            'mission_text_2': 'Trabajamos para abordar las causas fundamentales del conflicto, la violencia y la inseguridad en la región, fomentando soluciones colaborativas que respeten los derechos humanos y promuevan el desarrollo sostenible.',
            'our_vision': 'Nuestra Visión',
            'vision_text': 'Visualizamos una América Latina donde todas las comunidades vivan en paz y seguridad, donde los conflictos se resuelvan a través del diálogo en lugar de la violencia, y donde los ciudadanos participen activamente en la construcción de sociedades inclusivas, justas y democráticas.',
            'mission_image': 'Imagen de la Misión',

            // Contact Page
            'contact_title': 'Contáctenos',
            'contact_subtitle': 'Comuníquese para conocer más sobre nuestro trabajo o cómo participar con ILAPS',
            'get_in_touch': 'Póngase en Contacto',
            'get_in_touch_subtitle': 'Recibimos sus preguntas, comentarios y consultas',
            'contact_information': 'Información de Contacto',
            'headquarters': 'Sede Principal',
            'phone': 'Teléfono',
            'working_hours': 'Horario de Atención',
            'working_hours_text': 'Lunes – Viernes: 8:30 AM – 5:30 PM\nSábado y Domingo: Cerrado',
            'send_message': 'Envíenos un Mensaje',
            'subject': 'Asunto',
            'privacy_consent': 'Acepto el procesamiento de mis datos personales de acuerdo con la',
            'privacy_policy': 'Política de Privacidad',
            'map_placeholder': 'Mapa interactivo mostrando la sede principal y oficinas regionales de ILAPS',
            'regional_offices': 'Oficinas Regionales',
            'regional_offices_subtitle': 'Nuestra presencia en América Latina',
            'colombia_office': 'Colombia - Sede Principal',
            'mexico_office': 'Oficina México',

            // WHAT WE DO
            'what_we_do_title': 'Qué Hacemos',
            'what_we_do_subtitle': 'Conozca nuestros programas y proyectos que están generando un impacto en la región',
            'our_scope': 'Nuestro Alcance',
            'p1_text': 'En ILAPS creemos que la paz y la seguridad sostenibles en América Latina requieren enfoques integrales que aborden los retos interconectados a los que se enfrenta la región.',
            'p2_text': 'Nuestro trabajo se guía por varios principios clave:',
            
            // Our Approach
            'our_approach': 'Nuestro Enfoque',
            'local_ownership': 'Apropiación Local',
            'local_ownership_text': 'Las soluciones deben ser lideradas por actores locales con profundo entendimiento del contexto.',
            
            // Program Areas
            'program_areas': 'Áreas de Programa',
            'program_areas_subtitle': 'Áreas de enfoque estratégico que guían nuestras iniciativas',
            'conflict_resolution_program': 'Resolución de Conflictos',
            'conflict_resolution_program_text': 'Apoyando procesos de paz e iniciativas de diálogo para abordar conflictos políticos, sociales y comunitarios.',
            'mediation_training': 'Capacitación en mediación',
            'dialogue_facilitation': 'Facilitación de diálogo',
            'peace_process_support': 'Apoyo a procesos de paz',
            
            // Current Initiatives
            'current_initiatives': 'Iniciativas Actuales',
            'current_initiatives_subtitle': 'Programas destacados que generan impacto en América Latina',
            'colombia_peace': 'Construyendo Paz Sostenible en Colombia',
            'colombia_peace_location': 'Colombia',
            'colombia_peace_text': 'Apoyando la implementación del acuerdo de paz a través de programas de reconciliación comunitaria, apoyo a la reintegración de excombatientes y fortalecimiento de la gobernanza local en áreas afectadas por el conflicto.',
            'communities': 'Comunidades',
            'participants': 'Participantes',
            'years': 'Años',
            
            'border_security': 'Cooperación en Seguridad Fronteriza Regional',
            'border_security_location': 'Múltiples Países',
            'border_security_text': 'Facilitando la cooperación entre agencias de seguridad fronteriza para abordar desafíos transnacionales mientras se respetan los derechos humanos y se promueve la seguridad comunitaria en regiones fronterizas.',
            'countries': 'Países',
            'agencies': 'Agencias',
            
            'youth_leadership': 'Red de Liderazgo Juvenil para la Paz',
            'youth_leadership_location': 'Regional',
            'youth_leadership_text': 'Construyendo una red de jóvenes líderes comprometidos con la promoción de la paz y la prevención de la violencia en sus comunidades a través de capacitación, mentoría y proyectos colaborativos.',
            'young_leaders': 'Jóvenes Líderes',
            
            'security_forum_initiative': 'Foro de Seguridad Latinoamericano',
            'security_forum_location': 'Sedes Rotativas',
            'security_forum_text': 'Reunión anual de expertos en seguridad, formuladores de políticas y representantes de la sociedad civil para discutir desafíos emergentes y coordinar respuestas a problemas de seguridad regional.',
            
            // Impact
            'our_impact': 'Nuestro Impacto',
            'impact_subtitle': 'Cambio medible en toda la región',
            'active_programs': 'Países con Programas Activos',
            'people_impacted': 'Personas Impactadas Directamente',
            'local_partners': 'Socios Locales',
            
            'impact_highlights': 'Destacados de Impacto',
            'colombia_reconciliation': 'Reconciliación Comunitaria en Colombia',
            'colombia_reconciliation_text': 'Nuestros programas de diálogo comunitario han facilitado la reconciliación en más de 50 comunidades afectadas por el conflicto, con 85% de participantes reportando mejoras en las relaciones locales.',
            'enhanced_cooperation': 'Cooperación Regional Mejorada',
            
            // Resources
            'resources': 'Recursos',
            'resources_subtitle': 'Investigación, herramientas y publicaciones para apoyar el trabajo de paz y seguridad',
            'publications': 'Publicaciones',
            'publications_text': 'Informes de investigación, resúmenes de políticas y análisis sobre temas clave de paz y seguridad en América Latina.',
            'browse_publications': 'Explorar Publicaciones',
            
            // FAQ Page
            'faq_title': 'Preguntas Frecuentes',
            'faq_subtitle': 'Encuentre respuestas a preguntas comunes sobre ILAPS y nuestro trabajo',
            'didnt_find_answer': '¿No Encontró su Respuesta?',
            'didnt_find_answer_text': 'Si no encontró la información que buscaba, no dude en contactarnos directamente. Nuestro equipo está listo para ayudarle.',
            'email_us': 'Envíenos un Email',
            'call_us': 'Llámenos',

            // Blog Page
            'blog_title': 'Últimas Noticias y Perspectivas',
            'blog_subtitle': 'Manténgase informado sobre nuestro trabajo e impacto en América Latina',
            'read_more': 'Leer Más',
            'categories': 'Categorías',
            'recent_posts': 'Publicaciones Recientes',
            'archives': 'Archivos'
        }
    };
    
    // Language switcher
    const setupLanguageSwitcher = () => {
        // Default language
        let currentLanguage = localStorage.getItem('ilaps-language') || 'en';
        
        // Language buttons
        const languageButtons = document.querySelectorAll('.language-btn');
        
        if (languageButtons.length) {
            // Set active state based on current language
            languageButtons.forEach(button => {
                const lang = button.getAttribute('data-language');
                if (lang === currentLanguage) {
                    button.classList.add('active');
                } else {
                    button.classList.remove('active');
                }
                
                // Add click event
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const newLang = this.getAttribute('data-language');
                    
                    if (newLang !== currentLanguage) {
                        currentLanguage = newLang;
                        
                        // Save to local storage
                        localStorage.setItem('ilaps-language', currentLanguage);
                        
                        // Update active state
                        languageButtons.forEach(btn => {
                            if (btn.getAttribute('data-language') === currentLanguage) {
                                btn.classList.add('active');
                            } else {
                                btn.classList.remove('active');
                            }
                        });
                        
                        // Update translations
                        applyTranslations(currentLanguage);
                    }
                });
            });
            
            // Initial translation
            applyTranslations(currentLanguage);
        }
    };
    
    // Apply translations based on the selected language
    const applyTranslations = (language) => {
        const elements = document.querySelectorAll('[data-i18n]');
        
        elements.forEach(element => {
            const key = element.getAttribute('data-i18n');
            
            if (translations[language] && translations[language][key]) {
                if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                    element.placeholder = translations[language][key];
                } else {
                    element.textContent = translations[language][key];
                }
            }
        });
        
        // Update dynamic taglines
        updateTaglines(language);
    };
    
    // Dynamic Tagline Rotation
    const taglineRotator = () => {
        const taglineElement = document.getElementById('dynamic-tagline');
        
        if (taglineElement) {
            let currentIndex = 0;
            const language = localStorage.getItem('ilaps-language') || 'en';
            const taglines = getTaglines(language);
            
            // Initial tagline
            taglineElement.textContent = taglines[currentIndex];
            
            // Change tagline every 3 seconds
            setInterval(() => {
                currentIndex = (currentIndex + 1) % taglines.length;
                
                // Add fadeout class
                taglineElement.style.opacity = 0;
                taglineElement.style.transform = 'translateY(10px)';
                
                // After fadeout, change text and fadein
                setTimeout(() => {
                    const language = localStorage.getItem('ilaps-language') || 'en';
                    const taglines = getTaglines(language);
                    taglineElement.textContent = taglines[currentIndex];
                    taglineElement.style.opacity = 1;
                    taglineElement.style.transform = 'translateY(0)';
                }, 500);
            }, 3000);
        }
    };
    
    // Get taglines based on language
    const getTaglines = (language) => {
        return [
            translations[language]['tagline_1'],
            translations[language]['tagline_2'],
            translations[language]['tagline_3'],
            translations[language]['tagline_4'],
            translations[language]['tagline_5']
        ];
    };
    
    // Update taglines when language changes
    const updateTaglines = (language) => {
        const taglineElement = document.getElementById('dynamic-tagline');
        
        if (taglineElement) {
            const taglines = getTaglines(language);
            const currentText = taglineElement.textContent;
            
            // Find the corresponding tagline in the new language
            const currentIndex = Math.max(0, getTaglines('en').findIndex(t => t === currentText));
            taglineElement.textContent = taglines[currentIndex];
        }
    };
    
    // Fixed Navbar Scroll Effect
    const handleNavbarScroll = () => {
        const navbar = document.querySelector('.navbar');
        
        if (navbar) {
            window.addEventListener('scroll', () => {
                if (window.scrollY > 50) {
                    navbar.classList.add('navbar-scrolled');
                } else {
                    navbar.classList.remove('navbar-scrolled');
                }
            });
        }
    };
    
    // Handle contact form submission
    const setupContactForm = () => {
        const contactForm = document.getElementById('contact-form');
        const formFeedback = document.getElementById('form-feedback');
        
        if (contactForm && formFeedback) {
            contactForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // Get form data
                const formData = new FormData(contactForm);
                
                // Submit form using AJAX
                fetch('/submit-contact', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success message
                        formFeedback.innerHTML = `<div class="alert alert-success">${data.message}</div>`;
                        formFeedback.style.display = 'block';
                        
                        // Reset form
                        contactForm.reset();
                    } else {
                        // Show error message
                        formFeedback.innerHTML = `<div class="alert alert-danger">An error occurred. Please try again later.</div>`;
                        formFeedback.style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    formFeedback.innerHTML = `<div class="alert alert-danger">An error occurred. Please try again later.</div>`;
                    formFeedback.style.display = 'block';
                });
            });
        }
    };
    
    // Handle any modals that need to be initialized
    const initializeModals = () => {
        // Bootstrap will automatically handle most modal functionality
        // This function can be expanded if additional modal customization is needed
        
        // Initialize tooltips if needed
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    };
    
    // Run initialization functions
    setupLanguageSwitcher();
    taglineRotator();
    handleNavbarScroll();
    setupContactForm();
    initializeModals();
});

// Handle map errors
function handleMapError() {
    console.error('Map failed to load');
    const mapContainer = document.getElementById('map');
    if (mapContainer) {
        mapContainer.innerHTML = `
            <div class="map-error" style="padding: 20px; background-color: #f8d7da; color: #721c24; text-align: center;">
                <p>Unable to load the map. Please try again later.</p>
            </div>
        `;
    }
}
